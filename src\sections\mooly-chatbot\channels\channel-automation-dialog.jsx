'use client';

import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Switch from '@mui/material/Switch';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import FormControlLabel from '@mui/material/FormControlLabel';
import { alpha, useTheme } from '@mui/material/styles';

import { LoadingButton } from '@mui/lab';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import {
  getChannelAutomationConfig,
  upsertChannelAutomationConfig,
  createFollowupRule,
  createFollowupRuleWithFormatting,
  sortFollowupRules,
  formatDelayTime,
  convertFromMinutes,
  validateTimeValue,
  isDuplicateDelayTime,
  TIME_UNITS,
  MESSAGE_TEMPLATES
} from 'src/actions/mooly-chatbot/channel-automation-service';

import {
  MESSAGE_TYPES,
  generateMessagePreview,
  hasMessageFormatting
} from 'src/actions/mooly-chatbot/message-formatting-service';

import MessageFormattingForm from './components/message-formatting-form';

// ----------------------------------------------------------------------

export default function ChannelAutomationDialog({ open, onClose, channel }) {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  
  // State cho cấu hình
  const [isEnabled, setIsEnabled] = useState(true);
  const [followupRules, setFollowupRules] = useState([]);
  
  // State cho form thêm/edit rule
  const [showRuleForm, setShowRuleForm] = useState(false);
  const [editingRule, setEditingRule] = useState(null);
  const [ruleForm, setRuleForm] = useState({
    delayValue: 5,
    delayUnit: 'minutes',
    message: '',
    messageType: MESSAGE_TYPES.TEXT,
    messageFormatting: null
  });

  // Load cấu hình hiện tại khi mở dialog
  useEffect(() => {
    if (open && channel?.connection?.id) {
      loadAutomationConfig();
    }
  }, [open, channel?.connection?.id]);

  // Load cấu hình automation
  const loadAutomationConfig = async () => {
    setLoading(true);
    try {
      const channelId = channel?.connection?.id;
      if (!channelId) {
        console.warn('⚠️ Channel connection ID not found:', channel);
        toast.error('Không tìm thấy ID channel');
        return;
      }

      console.log('🔍 Loading automation config for channelId:', channelId);
      const result = await getChannelAutomationConfig(channelId);
      console.log('📄 Automation config result:', result);

      if (result.success) {
        if (result.data) {
          // Có dữ liệu - load cấu hình hiện tại
          console.log('✅ Found existing config:', result.data);
          setIsEnabled(result.data.isEnabled || false);
          setFollowupRules(sortFollowupRules(result.data.followupRules || []));
        } else {
          // Không có dữ liệu - cấu hình mặc định cho lần đầu
          console.log('📝 No existing config, using defaults');
          setIsEnabled(true);
          setFollowupRules([]);
        }
      } else {
        // Có lỗi thực sự
        console.error('❌ Error loading automation config:', result.error);
        toast.error('Không thể tải cấu hình automation');
        setIsEnabled(true);
        setFollowupRules([]);
      }
    } catch (error) {
      console.error('❌ Exception loading automation config:', error);
      toast.error('Không thể tải cấu hình automation');
    } finally {
      setLoading(false);
    }
  };

  // Lưu cấu hình
  const handleSave = async () => {
    setSaving(true);
    try {
      const channelId = channel?.connection?.id;
      if (!channelId) {
        toast.error('Không tìm thấy ID channel');
        return;
      }

      const result = await upsertChannelAutomationConfig(channelId, {
        isEnabled,
        followupRules: sortFollowupRules(followupRules)
      });

      if (result.success) {
        toast.success('Lưu cấu hình automation thành công!');
        onClose();
      } else {
        toast.error(`Lỗi: ${result.error}`);
      }
    } catch (error) {
      console.error('Error saving automation config:', error);
      toast.error('Không thể lưu cấu hình automation');
    } finally {
      setSaving(false);
    }
  };

  // Reset form
  const resetRuleForm = () => {
    setRuleForm({
      delayValue: 5,
      delayUnit: 'minutes',
      message: '',
      messageType: MESSAGE_TYPES.TEXT,
      messageFormatting: null
    });
    setEditingRule(null);
    setShowRuleForm(false);
  };

  // Mở form thêm rule mới
  const handleAddRule = () => {
    resetRuleForm();
    setShowRuleForm(true);
  };

  // Mở form edit rule
  const handleEditRule = (rule) => {
    const { value, unit } = convertFromMinutes(rule.delayMinutes);
    setRuleForm({
      delayValue: value,
      delayUnit: unit,
      message: rule.message,
      messageType: rule.messageType || MESSAGE_TYPES.TEXT,
      messageFormatting: rule.messageFormatting || null
    });
    setEditingRule(rule);
    setShowRuleForm(true);
  };

  // Lưu rule (thêm mới hoặc cập nhật)
  const handleSaveRule = () => {
    // Validate thời gian
    const timeValidation = validateTimeValue(ruleForm.delayValue, ruleForm.delayUnit);
    if (!timeValidation.isValid) {
      toast.error(timeValidation.error);
      return;
    }

    // Validate message
    if (!ruleForm.message.trim()) {
      toast.error('Nội dung tin nhắn không được để trống');
      return;
    }

    // Kiểm tra trùng thời gian
    const isDuplicate = isDuplicateDelayTime(
      followupRules,
      timeValidation.minutes,
      editingRule?.id
    );

    if (isDuplicate) {
      toast.error(`Thời gian ${formatDelayTime(timeValidation.minutes)} đã được sử dụng. Vui lòng chọn thời gian khác.`);
      return;
    }

    if (editingRule) {
      // Cập nhật rule hiện tại
      const updatedRules = followupRules.map(rule =>
        rule.id === editingRule.id
          ? {
              ...rule,
              delayMinutes: timeValidation.minutes,
              message: ruleForm.message,
              messageType: ruleForm.messageType,
              messageFormatting: ruleForm.messageFormatting
            }
          : rule
      );
      setFollowupRules(sortFollowupRules(updatedRules));
    } else {
      // Thêm rule mới với message formatting
      const messageData = {
        content: ruleForm.message,
        messageType: ruleForm.messageType,
        messageFormatting: ruleForm.messageFormatting
      };

      const rule = createFollowupRuleWithFormatting(
        timeValidation.minutes,
        messageData
      );
      setFollowupRules(sortFollowupRules([...followupRules, rule]));
    }

    resetRuleForm();
  };

  // Xóa rule
  const handleDeleteRule = (ruleId) => {
    setFollowupRules(prev => prev.filter(rule => rule.id !== ruleId));
  };

  // Toggle rule
  const handleToggleRule = (ruleId) => {
    setFollowupRules(prev =>
      prev.map(rule =>
        rule.id === ruleId ? { ...rule, isEnabled: !rule.isEnabled } : rule
      )
    );
  };

  // Sử dụng template tin nhắn
  const handleUseTemplate = (template) => {
    setRuleForm(prev => ({ ...prev, message: template }));
  };

  // Handle message formatting change
  const handleMessageFormattingChange = (messageData) => {
    setRuleForm(prev => ({
      ...prev,
      message: messageData.message || '',
      messageType: messageData.messageType || MESSAGE_TYPES.TEXT,
      messageFormatting: messageData.messageFormatting || null
    }));
  };

  // Reset form khi đóng dialog
  const handleClose = () => {
    resetRuleForm();
    onClose();
  };

  if (!channel) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: { height: '80vh' }
        }
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:settings-bold-duotone" width={24} />
          <Box>
            <Typography variant="h6">Cấu hình Automation Follow-up</Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {channel.name}
            </Typography>
          </Box>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ pb: 1 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Typography>Đang tải cấu hình...</Typography>
          </Box>
        ) : (
          <Stack spacing={3}>
            {/* Bật/tắt automation */}
            <Card sx={{ p: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isEnabled}
                    onChange={(e) => setIsEnabled(e.target.checked)}
                    color="success"
                  />
                }
                label={
                  <Box>
                    <Typography variant="subtitle2">
                      Bật tính năng Automation Follow-up
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Tự động gửi tin nhắn bám đuổi khách hàng sau khoảng thời gian không tương tác
                    </Typography>
                  </Box>
                }
              />
            </Card>

            {/* Danh sách rules */}
            <Box>
              <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6">Quy tắc Follow-up</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<Iconify icon="eva:plus-fill" />}
                  onClick={handleAddRule}
                  disabled={!isEnabled}
                >
                  Thêm quy tắc
                </Button>
              </Stack>

              {followupRules.length === 0 ? (
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Chưa có quy tắc nào. Hãy thêm quy tắc đầu tiên!
                  </Typography>
                </Card>
              ) : (
                <Stack spacing={2}>
                  {followupRules.map((rule, index) => (
                    <Card
                      key={rule.id}
                      sx={{
                        p: 2,
                        opacity: rule.isEnabled ? 1 : 0.6,
                        border: rule.isEnabled ? `1px solid ${alpha(theme.palette.success.main, 0.3)}` : undefined
                      }}
                    >
                      <Stack direction="row" alignItems="flex-start" spacing={2}>
                        <Chip
                          label={index + 1}
                          size="small"
                          color="primary"
                          sx={{ mt: 0.5 }}
                        />
                        
                        <Box flexGrow={1}>
                          <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                            <Chip
                              label={formatDelayTime(rule.delayMinutes)}
                              size="small"
                              variant="outlined"
                              color="info"
                            />
                            <Switch
                              size="small"
                              checked={rule.isEnabled}
                              onChange={() => handleToggleRule(rule.id)}
                              disabled={!isEnabled}
                            />
                          </Stack>

                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {generateMessagePreview(rule)}
                          </Typography>

                          {/* Message formatting indicators */}
                          {hasMessageFormatting(rule) && (
                            <Stack direction="row" spacing={0.5} mt={0.5}>
                              {rule.messageFormatting?.images?.length > 0 && (
                                <Chip
                                  size="small"
                                  label={`${rule.messageFormatting.images.length} hình`}
                                  color="info"
                                  variant="outlined"
                                  sx={{ fontSize: '0.75rem', height: 20 }}
                                />
                              )}
                              {rule.messageFormatting?.buttons?.length > 0 && (
                                <Chip
                                  size="small"
                                  label={`${rule.messageFormatting.buttons.length} nút`}
                                  color="success"
                                  variant="outlined"
                                  sx={{ fontSize: '0.75rem', height: 20 }}
                                />
                              )}
                            </Stack>
                          )}
                        </Box>

                        <Stack direction="row" spacing={0.5}>
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleEditRule(rule)}
                            disabled={!isEnabled}
                          >
                            <Iconify icon="eva:edit-fill" width={16} />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteRule(rule.id)}
                          >
                            <Iconify icon="eva:trash-2-fill" width={16} />
                          </IconButton>
                        </Stack>
                      </Stack>
                    </Card>
                  ))}
                </Stack>
              )}
            </Box>

            {/* Form thêm/edit rule */}
            {showRuleForm && (
              <Card sx={{ p: 2, border: `2px dashed ${theme.palette.primary.main}` }}>
                <Typography variant="subtitle2" mb={2}>
                  {editingRule ? 'Chỉnh sửa quy tắc' : 'Thêm quy tắc mới'}
                </Typography>

                <Stack spacing={2}>
                  {/* Input thời gian với đơn vị kế bên */}
                  <Box>
                    <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                      Thời gian delay
                    </Typography>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <TextField
                        type="number"
                        value={ruleForm.delayValue}
                        onChange={(e) => setRuleForm(prev => ({
                          ...prev,
                          delayValue: parseInt(e.target.value, 10) || 1
                        }))}
                        size="small"
                        slotProps={{
                          htmlInput: {
                            min: 1,
                            max: ruleForm.delayUnit === 'days' ? 30 : (ruleForm.delayUnit === 'hours' ? 720 : 43200)
                          }
                        }}
                        sx={{ width: 80 }}
                      />
                      <TextField
                        select
                        value={ruleForm.delayUnit}
                        onChange={(e) => {
                          const newUnit = e.target.value;
                          setRuleForm(prev => ({
                            ...prev,
                            delayUnit: newUnit,
                            delayValue: 1
                          }));
                        }}
                        size="small"
                        sx={{ minWidth: 100 }}
                      >
                        {TIME_UNITS.map((unit) => (
                          <MenuItem key={unit.value} value={unit.value}>
                            {unit.label}
                          </MenuItem>
                        ))}
                      </TextField>
                    </Stack>
                  </Box>

                  {/* Message Formatting Form */}
                  <MessageFormattingForm
                    value={{
                      message: ruleForm.message,
                      messageType: ruleForm.messageType,
                      messageFormatting: ruleForm.messageFormatting
                    }}
                    onChange={handleMessageFormattingChange}
                    disabled={false}
                    showTemplates
                  />

                  {/* Actions */}
                  <Stack direction="row" spacing={1} justifyContent="flex-end">
                    <Button
                      size="small"
                      onClick={resetRuleForm}
                    >
                      Hủy
                    </Button>
                    <Button
                      size="small"
                      variant="contained"
                      onClick={handleSaveRule}
                      disabled={!ruleForm.message.trim()}
                    >
                      {editingRule ? 'Cập nhật' : 'Thêm'}
                    </Button>
                  </Stack>
                </Stack>
              </Card>
            )}
          </Stack>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={handleClose}>
          Hủy
        </Button>
        <LoadingButton
          variant="contained"
          onClick={handleSave}
          loading={saving}
          disabled={loading}
        >
          Lưu cấu hình
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}

ChannelAutomationDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  channel: PropTypes.object,
};
