'use client';

import { storageService } from './storage-service';
import { supabase } from 'src/lib/supabase';

/**
 * Service xử lý upload hình ảnh cho message formatting
 * Tái sử dụng storageService hiện tại với cấu hình tối ưu
 */

// =====================================================
// 1. CONSTANTS
// =====================================================

export const MESSAGE_IMAGE_CONFIG = {
  BUCKET: 'public',
  FOLDER: 'message-images',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  MAX_IMAGES: 3
};

// =====================================================
// 2. VALIDATION FUNCTIONS
// =====================================================

/**
 * Validate image file
 * @param {File} file - File to validate
 * @returns {Object} - Validation result
 */
export function validateImageFile(file) {
  const errors = [];

  // Check file type
  if (!MESSAGE_IMAGE_CONFIG.ALLOWED_TYPES.includes(file.type)) {
    errors.push(`Định dạng file không được hỗ trợ. Chỉ chấp nhận: ${MESSAGE_IMAGE_CONFIG.ALLOWED_TYPES.join(', ')}`);
  }

  // Check file size
  if (file.size > MESSAGE_IMAGE_CONFIG.MAX_FILE_SIZE) {
    const maxSizeMB = MESSAGE_IMAGE_CONFIG.MAX_FILE_SIZE / (1024 * 1024);
    errors.push(`Kích thước file không được vượt quá ${maxSizeMB}MB`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate multiple image files
 * @param {Array} files - Array of files to validate
 * @returns {Object} - Validation result
 */
export function validateImageFiles(files) {
  const errors = [];

  // Check number of files
  if (files.length > MESSAGE_IMAGE_CONFIG.MAX_IMAGES) {
    errors.push(`Chỉ được upload tối đa ${MESSAGE_IMAGE_CONFIG.MAX_IMAGES} hình ảnh`);
  }

  // Validate each file
  files.forEach((file, index) => {
    const fileValidation = validateImageFile(file);
    if (!fileValidation.isValid) {
      errors.push(`File ${index + 1}: ${fileValidation.errors.join(', ')}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

// =====================================================
// 3. UPLOAD FUNCTIONS
// =====================================================

/**
 * Get tenant ID for current user
 * @returns {Promise<string|null>} - Tenant ID
 */
async function getCurrentTenantId() {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return null;
    }

    // Lấy tenant_id từ bảng users
    const { data } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    return data?.tenant_id || null;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    return null;
  }
}

/**
 * Upload single image for message formatting
 * @param {File} imageFile - Image file to upload
 * @returns {Promise<Object>} - Upload result
 */
export async function uploadMessageImage(imageFile) {
  try {
    // Validate file
    const validation = validateImageFile(imageFile);
    if (!validation.isValid) {
      return {
        success: false,
        error: { message: validation.errors.join(', ') },
        data: null
      };
    }

    // Get tenant ID
    const tenantId = await getCurrentTenantId();
    if (!tenantId) {
      return {
        success: false,
        error: { message: 'Không thể xác định tenant ID' },
        data: null
      };
    }

    // Generate unique filename
    const fileName = storageService.generateUniqueFileName(imageFile.name);
    
    // Build file path with tenant isolation
    const filePath = storageService.buildFilePath(MESSAGE_IMAGE_CONFIG.FOLDER, tenantId, fileName);

    console.log('Uploading message image to path:', filePath);

    // Upload using existing storageService
    const uploadResult = await storageService.uploadFile(
      MESSAGE_IMAGE_CONFIG.BUCKET, 
      filePath, 
      imageFile, 
      {
        upsert: true,
        cacheControl: '3600',
        contentType: imageFile.type
      }
    );

    if (!uploadResult.success) {
      console.error('Failed to upload message image:', uploadResult.error);
      return {
        success: false,
        error: uploadResult.error,
        data: null
      };
    }

    console.log('Message image uploaded successfully:', uploadResult.publicUrl);

    return {
      success: true,
      data: {
        id: `img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        url: uploadResult.publicUrl,
        alt: imageFile.name,
        fileName,
        filePath,
        uploadedAt: new Date().toISOString()
      },
      error: null
    };

  } catch (error) {
    console.error('Error uploading message image:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi upload hình ảnh' },
      data: null
    };
  }
}

/**
 * Upload multiple images for message formatting
 * @param {Array<File>} imageFiles - Array of image files to upload
 * @returns {Promise<Object>} - Upload result
 */
export async function uploadMessageImages(imageFiles) {
  try {
    // Validate files
    const validation = validateImageFiles(imageFiles);
    if (!validation.isValid) {
      return {
        success: false,
        error: { message: validation.errors.join(', ') },
        data: null
      };
    }

    // Upload each file
    const uploadPromises = imageFiles.map(file => uploadMessageImage(file));
    const results = await Promise.all(uploadPromises);

    // Check for failures
    const failedUploads = results.filter(result => !result.success);
    const successfulUploads = results.filter(result => result.success);

    if (failedUploads.length > 0) {
      // Some uploads failed
      const errorMessages = failedUploads.map(result => result.error?.message || 'Unknown error');
      return {
        success: false,
        error: { message: `Một số file upload thất bại: ${errorMessages.join(', ')}` },
        data: {
          successful: successfulUploads.map(result => result.data),
          failed: failedUploads.length
        }
      };
    }

    // All uploads successful
    return {
      success: true,
      data: successfulUploads.map(result => result.data),
      error: null
    };

  } catch (error) {
    console.error('Error uploading message images:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi upload hình ảnh' },
      data: null
    };
  }
}

// =====================================================
// 4. DELETE FUNCTIONS
// =====================================================

/**
 * Delete message image from storage
 * @param {string} filePath - File path in storage
 * @returns {Promise<Object>} - Delete result
 */
export async function deleteMessageImage(filePath) {
  try {
    const deleteResult = await storageService.deleteFile(MESSAGE_IMAGE_CONFIG.BUCKET, filePath);
    
    if (!deleteResult.success) {
      console.error('Failed to delete message image:', deleteResult.error);
      return {
        success: false,
        error: deleteResult.error
      };
    }

    console.log('Message image deleted successfully:', filePath);
    return {
      success: true,
      error: null
    };

  } catch (error) {
    console.error('Error deleting message image:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi xóa hình ảnh' }
    };
  }
}

/**
 * Delete multiple message images from storage
 * @param {Array<string>} filePaths - Array of file paths in storage
 * @returns {Promise<Object>} - Delete result
 */
export async function deleteMessageImages(filePaths) {
  try {
    const deletePromises = filePaths.map(filePath => deleteMessageImage(filePath));
    const results = await Promise.all(deletePromises);

    const failedDeletes = results.filter(result => !result.success);

    if (failedDeletes.length > 0) {
      console.error('Some message images failed to delete:', failedDeletes);
      return {
        success: false,
        error: { message: `Một số hình ảnh không thể xóa: ${failedDeletes.length} file` }
      };
    }

    console.log('All message images deleted successfully');
    return {
      success: true,
      error: null
    };

  } catch (error) {
    console.error('Error deleting message images:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi xóa hình ảnh' }
    };
  }
}

// =====================================================
// 5. UTILITY FUNCTIONS
// =====================================================

/**
 * Extract file path from public URL
 * @param {string} publicUrl - Public URL of the image
 * @returns {string|null} - File path or null if invalid
 */
export function extractFilePathFromUrl(publicUrl) {
  try {
    const url = new URL(publicUrl);
    const pathParts = url.pathname.split('/');
    
    // Remove empty parts and 'storage/v1/object/public/public'
    const cleanParts = pathParts.filter(part => part && part !== 'storage' && part !== 'v1' && part !== 'object' && part !== 'public');
    
    // Remove the first 'public' (bucket name)
    if (cleanParts[0] === 'public') {
      cleanParts.shift();
    }
    
    return cleanParts.join('/');
  } catch (error) {
    console.error('Error extracting file path from URL:', error);
    return null;
  }
}
